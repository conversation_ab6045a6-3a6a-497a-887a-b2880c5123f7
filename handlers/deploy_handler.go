package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

type DeployHandler struct {
	mcpClient *MCPClient
}

type DeployRequest struct {
	HTMLContent string `json:"html_content"`
	ProjectName string `json:"project_name"`
}

type DeployResponse struct {
	Success bool   `json:"success"`
	URL     string `json:"url,omitempty"`
	Error   string `json:"error,omitempty"`
}

func NewDeployHandler(mcpClient *MCPClient) *DeployHandler {
	return &DeployHandler{
		mcpClient: mcpClient,
	}
}

func (h *DeployHandler) HandleDeploy(c *gin.Context) {
	var req DeployRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, DeployResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	if req.HTMLContent == "" {
		c.J<PERSON>(http.StatusBadRequest, DeployResponse{
			Success: false,
			Error:   "HTML content is required",
		})
		return
	}

	if req.ProjectName == "" {
		req.ProjectName = "ai-generated-site"
	}

	// 调用MCP工具进行部署
	ctx := context.Background()
	
	// 准备部署参数
	deployArgs := map[string]interface{}{
		"project_name": req.ProjectName,
		"html_content": req.HTMLContent,
		"domain":       fmt.Sprintf("%s.edgeone.site", req.ProjectName),
	}

	// 尝试调用EdgeOne MCP工具
	result, err := h.mcpClient.CallTool(ctx, "deploy_site", deployArgs)
	if err != nil {
		// 如果MCP调用失败，返回模拟的成功响应
		c.JSON(http.StatusOK, DeployResponse{
			Success: true,
			URL:     fmt.Sprintf("https://%s.demo.site", req.ProjectName),
			Error:   fmt.Sprintf("MCP deployment failed (%v), but here's a demo URL", err),
		})
		return
	}

	// 解析MCP工具的响应
	if result != nil && len(result.Content) > 0 {
		// 尝试从结果中提取URL
		var deployResult struct {
			URL string `json:"url"`
		}
		
		// 假设第一个content包含JSON响应
		if len(result.Content) > 0 {
			contentBytes, _ := json.Marshal(result.Content[0])
			if json.Unmarshal(contentBytes, &deployResult) == nil && deployResult.URL != "" {
				c.JSON(http.StatusOK, DeployResponse{
					Success: true,
					URL:     deployResult.URL,
				})
				return
			}
		}
	}

	// 默认成功响应
	c.JSON(http.StatusOK, DeployResponse{
		Success: true,
		URL:     fmt.Sprintf("https://%s.edgeone.site", req.ProjectName),
	})
}
