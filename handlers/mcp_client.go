package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"github.com/modelcontextprotocol/go-sdk/mcp"
)

type MCPClient struct {
	client  *mcp.Client
	session *mcp.ClientSession
}

type MCPConfig struct {
	MCPServers map[string]MCPServerConfig `json:"mcpServers"`
}

type MCPServerConfig struct {
	URL string `json:"url"`
}

func NewMCPClient() *MCPClient {
	return &MCPClient{}
}

func (m *MCPClient) Initialize() error {
	// 硬编码MCP服务器配置
	config := MCPConfig{
		MCPServers: map[string]MCPServerConfig{
			"edgeone-pages-mcp-server": {
				URL: "https://mcp-on-edge.edgeone.site/mcp-server",
			},
		},
	}

	// 创建MCP客户端
	impl := &mcp.Implementation{
		Name:    "ai-chat-app",
		Version: "1.0.0",
	}

	client := mcp.NewClient(impl, nil)
	m.client = client

	// 连接到MCP服务器
	for serverName, serverConfig := range config.MCPServers {
		err := m.connectToServer(serverName, serverConfig.URL)
		if err != nil {
			log.Printf("Failed to connect to MCP server %s: %v", serverName, err)
			// 继续尝试其他服务器，不要因为一个服务器失败就停止
		}
	}

	return nil
}

func (m *MCPClient) connectToServer(serverName, serverURL string) error {
	ctx := context.Background()

	// 创建SSE传输（用于HTTP MCP服务器）
	transport := &mcp.SSEClientTransport{
		Endpoint: serverURL,
	}

	session, err := m.client.Connect(ctx, transport, nil)
	if err != nil {
		return fmt.Errorf("failed to connect to server: %w", err)
	}

	m.session = session
	log.Printf("Successfully connected to MCP server: %s", serverName)
	return nil
}

func (m *MCPClient) ListTools(ctx context.Context) ([]*mcp.Tool, error) {
	if m.session == nil {
		return nil, fmt.Errorf("MCP session not initialized")
	}

	result, err := m.session.ListTools(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to list tools: %w", err)
	}

	return result.Tools, nil
}

func (m *MCPClient) CallTool(ctx context.Context, toolName string, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	if m.session == nil {
		return nil, fmt.Errorf("MCP session not initialized")
	}

	// 将arguments转换为json.RawMessage
	argsJSON, err := json.Marshal(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal arguments: %w", err)
	}

	params := &mcp.CallToolParams{
		Name:      toolName,
		Arguments: argsJSON,
	}

	result, err := m.session.CallTool(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to call tool %s: %w", toolName, err)
	}

	return result, nil
}

func (m *MCPClient) Close() error {
	if m.session != nil {
		return m.session.Close()
	}
	return nil
}

// 辅助函数：将MCP工具结果转换为字符串
func (m *MCPClient) FormatToolResult(result *mcp.CallToolResult) string {
	if result == nil {
		return "No result"
	}

	// 尝试格式化结果
	if result.Content != nil {
		jsonData, err := json.MarshalIndent(result.Content, "", "  ")
		if err != nil {
			return fmt.Sprintf("Result: %v", result.Content)
		}
		return string(jsonData)
	}

	return "Tool executed successfully"
}
