package handlers

import (
	"html/template"
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
)

type PreviewHandler struct{}

type PreviewRequest struct {
	HTMLContent string `json:"html_content"`
}

func NewPreviewHandler() *PreviewHandler {
	return &PreviewHandler{}
}

// HandlePreview 处理HTML预览请求
func (h *PreviewHandler) HandlePreview(c *gin.Context) {
	var req PreviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	// 清理和验证HTML内容
	cleanHTML := h.sanitizeHTML(req.HTMLContent)
	
	// 返回清理后的HTML
	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(http.StatusOK, cleanHTML)
}

// HandlePreviewPage 提供一个独立的预览页面
func (h *PreviewHandler) HandlePreviewPage(c *gin.Context) {
	htmlContent := c.Query("content")
	if htmlContent == "" {
		c.String(http.StatusBadRequest, "No HTML content provided")
		return
	}

	// 解码URL编码的内容
	cleanHTML := h.sanitizeHTML(htmlContent)
	
	// 如果不是完整的HTML文档，包装在基本的HTML结构中
	if !strings.Contains(strings.ToLower(cleanHTML), "<!doctype") && 
	   !strings.Contains(strings.ToLower(cleanHTML), "<html") {
		cleanHTML = h.wrapInHTMLDocument(cleanHTML)
	}

	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(http.StatusOK, cleanHTML)
}

// sanitizeHTML 基本的HTML清理（移除潜在危险的脚本）
func (h *PreviewHandler) sanitizeHTML(htmlContent string) string {
	// 移除潜在危险的标签和属性
	// 注意：这是一个基本的实现，生产环境中应该使用更完善的HTML sanitizer
	
	// 移除script标签
	scriptRegex := regexp.MustCompile(`(?i)<script[^>]*>.*?</script>`)
	cleanHTML := scriptRegex.ReplaceAllString(htmlContent, "")
	
	// 移除on*事件处理器
	eventRegex := regexp.MustCompile(`(?i)\s+on\w+\s*=\s*["'][^"']*["']`)
	cleanHTML = eventRegex.ReplaceAllString(cleanHTML, "")
	
	// 移除javascript: 协议
	jsProtocolRegex := regexp.MustCompile(`(?i)javascript:`)
	cleanHTML = jsProtocolRegex.ReplaceAllString(cleanHTML, "")
	
	return cleanHTML
}

// wrapInHTMLDocument 将HTML片段包装在完整的HTML文档中
func (h *PreviewHandler) wrapInHTMLDocument(content string) string {
	tmpl := `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML Preview</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #fff;
        }
        
        /* 基本样式重置 */
        * {
            box-sizing: border-box;
        }
        
        /* 响应式图片 */
        img {
            max-width: 100%;
            height: auto;
        }
        
        /* 基本表格样式 */
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
        }
        
        /* 基本表单样式 */
        input, textarea, select, button {
            padding: 8px;
            margin: 4px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
{{.Content}}
</body>
</html>`

	t, err := template.New("preview").Parse(tmpl)
	if err != nil {
		return content // 如果模板解析失败，返回原内容
	}

	var buf strings.Builder
	err = t.Execute(&buf, struct{ Content template.HTML }{Content: template.HTML(content)})
	if err != nil {
		return content // 如果模板执行失败，返回原内容
	}

	return buf.String()
}

// ExtractHTMLFromMarkdown 从markdown代码块中提取HTML
func (h *PreviewHandler) ExtractHTMLFromMarkdown(content string) string {
	// 匹配 ```html ... ``` 代码块
	htmlRegex := regexp.MustCompile(`(?s)` + "```html\\s*\\n(.*?)\\n```")
	matches := htmlRegex.FindStringSubmatch(content)
	
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}
	
	// 如果没有找到markdown代码块，检查是否直接包含HTML
	if strings.Contains(content, "<") && strings.Contains(content, ">") {
		return content
	}
	
	return ""
}
