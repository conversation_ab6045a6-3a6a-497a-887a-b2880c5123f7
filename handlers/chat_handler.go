package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

type ChatHandler struct {
	aiClient  *AIClient
	mcpClient *MCPClient
}

type WebChatRequest struct {
	Message string        `json:"message"`
	History []ChatMessage `json:"history"`
}

type WebChatResponse struct {
	Response string `json:"response"`
	Error    string `json:"error,omitempty"`
}

func NewChatHandler() *ChatHandler {
	return &ChatHandler{
		aiClient:  NewAIClient(),
		mcpClient: NewMCPClient(),
	}
}

func (h *ChatHandler) Initialize() error {
	return h.mcpClient.Initialize()
}

func (h *ChatHandler) HandleChat(c *gin.Context) {
	var req WebChatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, WebChatResponse{
			Error: "Invalid request format",
		})
		return
	}

	// 构建消息历史
	messages := make([]ChatMessage, 0, len(req.History)+1)

	// 添加系统提示
	systemPrompt := "你是一个AI助手，可以帮助用户回答问题和生成代码。当用户需要生成HTML代码时，请用markdown的代码块格式包装，例如：\n\n```html\n<!DOCTYPE html>\n<html>\n<head>\n    <title>示例</title>\n</head>\n<body>\n    <h1>Hello World</h1>\n</body>\n</html>\n```\n\n你还可以使用MCP工具来增强功能。如果用户的请求可能需要使用工具，请告诉我需要什么工具。"

	messages = append(messages, ChatMessage{
		Role:    "system",
		Content: systemPrompt,
	})

	// 添加历史消息
	messages = append(messages, req.History...)

	// 添加当前用户消息
	messages = append(messages, ChatMessage{
		Role:    "user",
		Content: req.Message,
	})

	// 检查是否需要使用MCP工具
	toolResponse := ""
	if h.shouldUseMCPTools(req.Message) {
		ctx := context.Background()
		tools, err := h.mcpClient.ListTools(ctx)
		if err == nil && len(tools) > 0 {
			// 这里可以根据用户请求智能选择合适的工具
			// 为简化，我们先列出可用工具
			toolNames := make([]string, 0, len(tools))
			for _, tool := range tools {
				toolNames = append(toolNames, tool.Name)
			}
			toolResponse = fmt.Sprintf("\n\n可用的MCP工具: %s", strings.Join(toolNames, ", "))
		}
	}

	// 调用AI API
	response, err := h.aiClient.Chat(messages)
	if err != nil {
		c.JSON(http.StatusInternalServerError, WebChatResponse{
			Error: fmt.Sprintf("AI API调用失败: %v", err),
		})
		return
	}

	if len(response.Choices) == 0 {
		c.JSON(http.StatusInternalServerError, WebChatResponse{
			Error: "AI API返回空响应",
		})
		return
	}

	aiResponse := response.Choices[0].Message.Content + toolResponse

	c.JSON(http.StatusOK, WebChatResponse{
		Response: aiResponse,
	})
}

func (h *ChatHandler) shouldUseMCPTools(message string) bool {
	// 简单的关键词检测，判断是否需要使用MCP工具
	keywords := []string{"工具", "tool", "功能", "执行", "运行", "查询", "搜索"}
	lowerMessage := strings.ToLower(message)

	for _, keyword := range keywords {
		if strings.Contains(lowerMessage, keyword) {
			return true
		}
	}
	return false
}

func (h *ChatHandler) HandleMCPTools(c *gin.Context) {
	ctx := context.Background()

	tools, err := h.mcpClient.ListTools(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("获取MCP工具失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"tools": tools,
	})
}

func (h *ChatHandler) Close() error {
	return h.mcpClient.Close()
}
