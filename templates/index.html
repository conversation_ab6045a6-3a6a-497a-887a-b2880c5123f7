<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            height: 100vh;
            display: flex;
        }

        .sidebar {
            width: 260px;
            background-color: #2d3748;
            color: white;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar h2 {
            margin-bottom: 20px;
            font-size: 18px;
        }

        .new-chat-btn {
            width: 100%;
            padding: 12px;
            background-color: #4a5568;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .new-chat-btn:hover {
            background-color: #5a6578;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            padding: 20px;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px 0;
            margin-bottom: 20px;
        }

        .message {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 8px;
            max-width: 80%;
        }

        .message.user {
            background-color: #e3f2fd;
            margin-left: auto;
            text-align: right;
        }

        .message.assistant {
            background-color: white;
            border: 1px solid #e0e0e0;
        }

        .message-content {
            white-space: pre-wrap;
            line-height: 1.5;
        }

        .html-preview {
            margin-top: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }

        .html-preview-header {
            padding: 8px 12px;
            background-color: #f0f0f0;
            border-bottom: 1px solid #ddd;
            font-size: 12px;
            color: #666;
        }

        .html-preview-content {
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
        }

        .input-container {
            display: flex;
            gap: 10px;
            padding: 20px;
            background-color: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-box {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            resize: vertical;
            min-height: 50px;
            max-height: 150px;
            font-family: inherit;
        }

        .send-btn {
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            height: fit-content;
        }

        .send-btn:hover {
            background-color: #0056b3;
        }

        .send-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <h2>AI Chat Assistant</h2>
        <button class="new-chat-btn" onclick="newChat()">+ 新对话</button>
        <div class="chat-history">
            <!-- 对话历史将在这里显示 -->
        </div>
    </div>

    <div class="main-content">
        <div class="chat-container">
            <div class="messages" id="messages">
                <div class="message assistant">
                    <div class="message-content">你好！我是AI助手，可以帮你回答问题和生成HTML代码。有什么我可以帮助你的吗？</div>
                </div>
            </div>
            
            <div class="loading" id="loading">
                AI正在思考中...
            </div>

            <div class="input-container">
                <textarea class="input-box" id="messageInput" placeholder="输入你的消息..." onkeydown="handleKeyDown(event)"></textarea>
                <button class="send-btn" id="sendBtn" onclick="sendMessage()">发送</button>
            </div>
        </div>
    </div>

    <script>
        let conversationHistory = [];

        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        function newChat() {
            conversationHistory = [];
            document.getElementById('messages').innerHTML = `
                <div class="message assistant">
                    <div class="message-content">你好！我是AI助手，可以帮你回答问题和生成HTML代码。有什么我可以帮助你的吗？</div>
                </div>
            `;
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;

            const sendBtn = document.getElementById('sendBtn');
            const loading = document.getElementById('loading');
            
            // 禁用发送按钮
            sendBtn.disabled = true;
            input.value = '';

            // 添加用户消息到界面
            addMessage('user', message);
            
            // 显示加载状态
            loading.style.display = 'block';

            try {
                // 发送请求到后端
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        history: conversationHistory
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                // 添加AI回复到界面
                addMessage('assistant', data.response);
                
                // 更新对话历史
                conversationHistory.push({role: 'user', content: message});
                conversationHistory.push({role: 'assistant', content: data.response});

            } catch (error) {
                console.error('Error:', error);
                addMessage('assistant', '抱歉，发生了错误：' + error.message);
            } finally {
                // 恢复发送按钮和隐藏加载状态
                sendBtn.disabled = false;
                loading.style.display = 'none';
            }
        }

        function addMessage(role, content) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(contentDiv);

            // 检查是否包含HTML代码并添加预览
            if (role === 'assistant' && isHTMLCode(content)) {
                const previewDiv = createHTMLPreview(content);
                messageDiv.appendChild(previewDiv);
            }

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function isHTMLCode(content) {
            // 简单检查是否包含HTML标签
            return content.includes('<html') || content.includes('<!DOCTYPE') || 
                   (content.includes('<') && content.includes('>') && 
                    (content.includes('<div') || content.includes('<p') || content.includes('<h')));
        }

        function createHTMLPreview(content) {
            const previewDiv = document.createElement('div');
            previewDiv.className = 'html-preview';
            
            const header = document.createElement('div');
            header.className = 'html-preview-header';
            header.textContent = 'HTML预览';
            
            const iframe = document.createElement('iframe');
            iframe.style.width = '100%';
            iframe.style.height = '300px';
            iframe.style.border = 'none';
            
            // 提取HTML代码
            const htmlMatch = content.match(/```html\s*([\s\S]*?)\s*```/) || 
                             content.match(/<html[\s\S]*<\/html>/) ||
                             [null, content];
            
            if (htmlMatch && htmlMatch[1]) {
                iframe.srcdoc = htmlMatch[1];
            } else if (content.includes('<') && content.includes('>')) {
                iframe.srcdoc = content;
            }
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'html-preview-content';
            contentDiv.appendChild(iframe);
            
            previewDiv.appendChild(header);
            previewDiv.appendChild(contentDiv);
            
            return previewDiv;
        }
    </script>
</body>
</html>
