<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Assistant</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .typing-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #6b7280;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-indicator:nth-child(1) {
            animation-delay: -0.32s;
        }

        .typing-indicator:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes typing {

            0%,
            80%,
            100% {
                transform: scale(0);
            }

            40% {
                transform: scale(1);
            }
        }

        .message-content pre {
            background-color: #f3f4f6;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            white-space: pre-wrap;
        }

        .message-content code {
            background-color: #f3f4f6;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        .preview-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 0.5rem;
        }

        .resizer {
            width: 4px;
            background-color: #e5e7eb;
            cursor: col-resize;
            transition: background-color 0.2s;
        }

        .resizer:hover {
            background-color: #9ca3af;
        }
    </style>
</head>

<body class="bg-gray-50 h-screen flex overflow-hidden">
    <!-- 侧边栏 -->
    <div class="w-64 bg-gray-900 text-white flex flex-col">
        <div class="p-4 border-b border-gray-700">
            <h2 class="text-lg font-semibold mb-4">AI Chat Assistant</h2>
            <button onclick="newChat()"
                class="w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors">
                + 新对话
            </button>
        </div>
        <div class="flex-1 p-4 overflow-y-auto">
            <div id="chatHistory" class="space-y-2">
                <!-- 对话历史将在这里显示 -->
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 flex">
        <!-- 聊天区域 -->
        <div id="chatArea" class="flex-1 flex flex-col min-w-0">
            <div class="flex-1 overflow-y-auto p-4">
                <div id="messages" class="max-w-4xl mx-auto space-y-4">
                    <div class="bg-white rounded-lg p-4 shadow-sm">
                        <div class="flex items-start space-x-3">
                            <div
                                class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                AI
                            </div>
                            <div class="flex-1">
                                <div class="text-gray-800">
                                    你好！我是AI助手，专门帮助你创建响应式网页。我会使用TailwindCSS和现代Web技术来创建美观的单页面应用。有什么我可以帮助你的吗？</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="border-t bg-white p-4">
                <div class="max-w-4xl mx-auto">
                    <div class="flex space-x-4">
                        <textarea id="messageInput" placeholder="输入你的消息..."
                            class="flex-1 resize-none border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            rows="3" onkeydown="handleKeyDown(event)"></textarea>
                        <button id="sendBtn" onclick="sendMessage()"
                            class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed">
                            发送
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 可调整大小的分隔条 -->
        <div id="resizer" class="resizer"></div>

        <!-- 预览区域 -->
        <div id="previewArea" class="w-1/2 bg-white border-l flex flex-col" style="display: none;">
            <div class="bg-gray-50 border-b p-4 flex items-center justify-between">
                <h3 class="font-semibold text-gray-800">HTML 预览</h3>
                <div class="flex space-x-2">
                    <button id="deployBtn" onclick="deployToEdgeOne()"
                        class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm transition-colors disabled:bg-gray-400">
                        部署到 EdgeOne
                    </button>
                    <button onclick="closePreview()"
                        class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                        关闭
                    </button>
                </div>
            </div>
            <div class="flex-1 p-4">
                <iframe id="previewFrame" class="preview-iframe"></iframe>
            </div>
        </div>
    </div>

    <script>
        let conversationHistory = [];
        let currentStreamingMessage = null;
        let currentHTMLContent = '';

        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        function newChat() {
            conversationHistory = [];
            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML = `
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                            AI
                        </div>
                        <div class="flex-1">
                            <div class="text-gray-800">你好！我是AI助手，专门帮助你创建响应式网页。我会使用TailwindCSS和现代Web技术来创建美观的单页面应用。有什么我可以帮助你的吗？</div>
                        </div>
                    </div>
                </div>
            `;
            closePreview();
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) return;

            const sendBtn = document.getElementById('sendBtn');

            // 禁用发送按钮
            sendBtn.disabled = true;
            sendBtn.textContent = '发送中...';
            input.value = '';

            // 添加用户消息到界面
            addMessage('user', message);

            // 创建AI消息容器用于流式输出
            const aiMessageDiv = createStreamingMessage();

            try {
                // 使用流式API
                const response = await fetch('/api/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        history: conversationHistory
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let aiResponse = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') {
                                break;
                            }
                            if (data.startsWith('error:')) {
                                throw new Error(data.slice(6));
                            }
                            if (data) {
                                aiResponse += data;
                                updateStreamingMessage(aiMessageDiv, aiResponse);
                            }
                        }
                    }
                }

                // 完成流式输出
                finishStreamingMessage(aiMessageDiv, aiResponse);

                // 更新对话历史
                conversationHistory.push({ role: 'user', content: message });
                conversationHistory.push({ role: 'assistant', content: aiResponse });

                // 检查是否包含HTML代码并显示预览
                if (isHTMLCode(aiResponse)) {
                    showPreview(aiResponse);
                }

            } catch (error) {
                console.error('Error:', error);
                updateStreamingMessage(aiMessageDiv, '抱歉，发生了错误：' + error.message);
                finishStreamingMessage(aiMessageDiv, '抱歉，发生了错误：' + error.message);
            } finally {
                // 恢复发送按钮
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';
            }
        }

        function addMessage(role, content) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'bg-white rounded-lg p-4 shadow-sm';

            const avatar = role === 'user' ?
                '<div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-medium">你</div>' :
                '<div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">AI</div>';

            messageDiv.innerHTML = `
                <div class="flex items-start space-x-3">
                    ${avatar}
                    <div class="flex-1">
                        <div class="text-gray-800 message-content">${formatMessage(content)}</div>
                    </div>
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            return messageDiv;
        }

        function createStreamingMessage() {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'bg-white rounded-lg p-4 shadow-sm';

            messageDiv.innerHTML = `
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">AI</div>
                    <div class="flex-1">
                        <div class="text-gray-800 message-content">
                            <span class="typing-indicator"></span>
                            <span class="typing-indicator"></span>
                            <span class="typing-indicator"></span>
                        </div>
                    </div>
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            currentStreamingMessage = messageDiv;

            return messageDiv;
        }

        function updateStreamingMessage(messageDiv, content) {
            const contentDiv = messageDiv.querySelector('.message-content');
            contentDiv.innerHTML = formatMessage(content) + '<span class="typing-indicator"></span>';

            const messagesContainer = document.getElementById('messages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function finishStreamingMessage(messageDiv, content) {
            const contentDiv = messageDiv.querySelector('.message-content');
            contentDiv.innerHTML = formatMessage(content);
            currentStreamingMessage = null;
        }

        function formatMessage(content) {
            // 简单的markdown格式化
            return content
                .replace(/```html\n([\s\S]*?)\n```/g, '<pre><code class="language-html">$1</code></pre>')
                .replace(/```(\w+)?\n([\s\S]*?)\n```/g, '<pre><code class="language-$1">$2</code></pre>')
                .replace(/`([^`]+)`/g, '<code>$1</code>')
                .replace(/\n/g, '<br>');
        }

        function isHTMLCode(content) {
            return content.includes('```html') ||
                (content.includes('<html') || content.includes('<!DOCTYPE'));
        }

        function showPreview(content) {
            const previewArea = document.getElementById('previewArea');
            const previewFrame = document.getElementById('previewFrame');
            const chatArea = document.getElementById('chatArea');

            // 提取HTML代码
            let htmlContent = '';
            const htmlMatch = content.match(/```html\s*([\s\S]*?)\s*```/);
            if (htmlMatch && htmlMatch[1]) {
                htmlContent = htmlMatch[1];
            } else if (content.includes('<html') || content.includes('<!DOCTYPE')) {
                htmlContent = content;
            }

            if (htmlContent) {
                currentHTMLContent = htmlContent;
                previewFrame.srcdoc = htmlContent;
                previewArea.style.display = 'flex';
                chatArea.style.width = '50%';

                // 启用部署按钮
                document.getElementById('deployBtn').disabled = false;
            }
        }

        function closePreview() {
            const previewArea = document.getElementById('previewArea');
            const chatArea = document.getElementById('chatArea');

            previewArea.style.display = 'none';
            chatArea.style.width = '100%';
            currentHTMLContent = '';
        }

        async function deployToEdgeOne() {
            if (!currentHTMLContent) {
                alert('没有可部署的HTML内容');
                return;
            }

            const deployBtn = document.getElementById('deployBtn');
            const originalText = deployBtn.textContent;

            deployBtn.disabled = true;
            deployBtn.textContent = '部署中...';

            try {
                const projectName = `ai-site-${Date.now()}`;
                const response = await fetch('/api/deploy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        html_content: currentHTMLContent,
                        project_name: projectName
                    })
                });

                const result = await response.json();

                if (result.success) {
                    alert(`部署成功！\n访问地址: ${result.url}`);
                    // 可以选择打开新窗口
                    if (confirm('是否在新窗口中打开部署的网站？')) {
                        window.open(result.url, '_blank');
                    }
                } else {
                    alert('部署失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                console.error('Deploy error:', error);
                alert('部署失败: ' + error.message);
            } finally {
                deployBtn.disabled = false;
                deployBtn.textContent = originalText;
            }
        }

        // 初始化拖拽调整大小功能
        function initResizer() {
            const resizer = document.getElementById('resizer');
            const chatArea = document.getElementById('chatArea');
            const previewArea = document.getElementById('previewArea');
            let isResizing = false;

            resizer.addEventListener('mousedown', (e) => {
                isResizing = true;
                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', () => {
                    isResizing = false;
                    document.removeEventListener('mousemove', handleMouseMove);
                });
            });

            function handleMouseMove(e) {
                if (!isResizing) return;

                const containerWidth = chatArea.parentElement.offsetWidth;
                const newChatWidth = e.clientX - chatArea.offsetLeft;
                const chatWidthPercent = (newChatWidth / containerWidth) * 100;
                const previewWidthPercent = 100 - chatWidthPercent;

                if (chatWidthPercent > 30 && previewWidthPercent > 30) {
                    chatArea.style.width = chatWidthPercent + '%';
                    previewArea.style.width = previewWidthPercent + '%';
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initResizer();
        });
    </script>
</body>

</html>