package main

import (
	"log"
	"net/http"

	"ai-chat-app/handlers"

	"github.com/gin-gonic/gin"
)

func main() {
	// 创建Gin路由器
	r := gin.Default()

	// 加载HTML模板
	r.LoadHTMLGlob("templates/*")

	// 静态文件服务
	r.Static("/static", "./static")

	// 创建处理器
	chatHandler := handlers.NewChatHandler()
	previewHandler := handlers.NewPreviewHandler()

	// 初始化MCP客户端
	if err := chatHandler.Initialize(); err != nil {
		log.Printf("Warning: Failed to initialize MCP client: %v", err)
		// 不要因为MCP初始化失败就停止应用，继续运行
	}

	// 创建部署处理器，共享MCP客户端
	deployHandler := handlers.NewDeployHandler(chatHandler.GetMCPClient())

	// 设置路由
	setupRoutes(r, chatHandler, previewHandler, deployHandler)

	// 启动服务器
	log.Println("Starting server on :8080...")
	log.Fatal(http.ListenAndServe(":8080", r))
}

func setupRoutes(r *gin.Engine, chatHandler *handlers.ChatHandler, previewHandler *handlers.PreviewHandler, deployHandler *handlers.DeployHandler) {
	// 主页
	r.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title": "AI Chat Assistant",
		})
	})

	// API路由
	api := r.Group("/api")
	{
		api.POST("/chat", chatHandler.HandleChat)
		api.POST("/chat/stream", chatHandler.HandleChatStream)
		api.GET("/mcp/tools", chatHandler.HandleMCPTools)
		api.POST("/preview", previewHandler.HandlePreview)
		api.GET("/preview", previewHandler.HandlePreviewPage)
		api.POST("/deploy", deployHandler.HandleDeploy)
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"service": "ai-chat-app",
		})
	})
}
